{"name": "codebattle-backend", "version": "1.0.0", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node utils/seedDatabase.js", "seed:problems": "node scripts/seedProblems.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["competitive-programming", "real-time", "coding-competition"], "author": "", "license": "ISC", "description": "Backend for CodeBattle - Real-time competitive coding platform", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.0", "socket.io": "^4.8.1"}, "devDependencies": {"autoprefixer": "^10.4.21", "nodemon": "^3.1.10", "postcss": "^8.5.6", "tailwindcss": "^3.4.17"}}